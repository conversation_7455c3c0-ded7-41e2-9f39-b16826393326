from pydantic import BaseModel, Field, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum

class InviteReceivedByEnum(str, Enum):
    mentor = "mentor"
    institute = "institute"


class MentorInstituteInvite(BaseModel):
    receiver_id: UUID
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    received_by: Optional[InviteReceivedByEnum] = None

    class Config:
        from_attributes = True
class MentorInstituteInviteOut(MentorInstituteInvite):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
class InvitationListResponse(BaseModel):
    invitations: List[MentorInstituteInviteOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True
