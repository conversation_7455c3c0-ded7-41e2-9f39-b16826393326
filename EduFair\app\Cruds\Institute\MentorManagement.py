from typing import List, Dict, Any, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, extract
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
from decimal import Decimal

# Import Models
from Models.users import (
    User, UserTypeEnum, InstituteProfile, MentorProfile,
    MentorInstituteAssociation, MentorInstituteApplication, MentorInstituteInvite
)
from Models.Events import Event
from Models.Competitions import CompetitionMentorAssignment, MentorAssignmentStatusEnum

# Import Schemas
from Schemas.Institute.MentorManagement import (
    MentorApplicationOut, MentorApplicationsResponse, MentorInvitationCreate,
    MentorInvitationOut, ApplicationApprovalRequest, ApplicationRejectionRequest,
    InstituteMentorOut, MentorPerformanceOut, MentorAssignmentCreate,
    MentorAssignmentOut, MentorActivationRequest, MentorUpdateRequest,
    MentorUtilizationReport, MentorSatisfactionReport, MentorPerformanceReportResponse,
    MentorApplicationsSummaryOut, InstituteMentorsResponse
)


def get_mentor_applications(
    db: Session,
    institute_id: uuid.UUID,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20
) -> MentorApplicationsResponse:
    """Get mentor invitations sent by institute"""

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Build query using MentorInstituteInvite model (invitations sent by institute)
    query = db.query(MentorInstituteInvite).options(
        joinedload(MentorInstituteInvite.mentor).joinedload(User.mentor_profile)
    ).filter(MentorInstituteInvite.institute_id == institute_id)

    if status:
        query = query.filter(MentorInstituteInvite.status == status)

    # Get total count
    total = query.count()

    # Get invitations with pagination
    applications = query.order_by(desc(MentorInstituteInvite.invited_at)).offset(skip).limit(limit).all()
    
    # Convert to response format
    application_list = []
    for invite in applications:
        mentor = invite.mentor
        mentor_profile = mentor.mentor_profile if mentor else None

        application_list.append(MentorApplicationOut(
            id=invite.id,
            applicant_id=invite.mentor_id,
            applicant_name=mentor.username if mentor else "Unknown",
            applicant_email=invite.mentor_email,
            status=invite.status,
            application_date=invite.invited_at,
            application_message=invite.invitation_message,
            expertise=mentor_profile.expertise_areas if mentor_profile and hasattr(mentor_profile, 'expertise_areas') else [],
            proposed_hourly_rate=invite.proposed_hourly_rate,
            availability_hours=invite.proposed_hours_per_week,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            education=getattr(mentor_profile, 'education', None) if mentor_profile else None,
            current_position=getattr(mentor_profile, 'current_position', None) if mentor_profile else None,
            linkedin_url=getattr(mentor_profile, 'linkedin_url', None) if mentor_profile else None,
            resume_url=getattr(mentor_profile, 'resume_url', None) if mentor_profile else None
        ))
    
    # Get status counts for invitations
    pending = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).count()

    approved = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "accepted"
    ).count()

    rejected = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "rejected"
    ).count()
    
    return MentorApplicationsResponse(
        applications=application_list,
        total=total,
        pending=pending,
        approved=approved,
        rejected=rejected
    )


def approve_mentor_application(
    db: Session,
    institute_id: uuid.UUID,
    application_id: uuid.UUID,
    approval_data: ApplicationApprovalRequest
) -> MentorApplicationOut:
    """Approve mentor application"""
    
    # Get application using the new MentorInstituteApplication model
    application = db.query(MentorInstituteApplication).options(
        joinedload(MentorInstituteApplication.mentor).joinedload(User.mentor_profile)
    ).filter(
        MentorInstituteApplication.id == application_id,
        MentorInstituteApplication.institute_id == institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if not application:
        raise HTTPException(status_code=404, detail="Application not found or already processed")

    # Update application
    application.status = "approved"
    application.responded_at = datetime.now(timezone.utc)
    application.response_message = approval_data.approval_message

    if approval_data.hourly_rate:
        application.proposed_hourly_rate = approval_data.hourly_rate
    if approval_data.start_date:
        application.start_date = approval_data.start_date
    if approval_data.contract_terms:
        application.contract_terms = approval_data.contract_terms
    
    db.commit()
    db.refresh(application)
    
    # Convert to response format
    mentor = application.mentor
    mentor_profile = mentor.mentor_profile
    
    return MentorApplicationOut(
        id=application.id,
        applicant_id=application.mentor_id,
        applicant_name=f"{mentor.first_name} {mentor.last_name}",
        applicant_email=mentor.email,
        status=application.status,
        application_date=application.applied_at,
        application_message=application.application_message,
        expertise=mentor_profile.expertise_areas if mentor_profile and mentor_profile.expertise_areas else [],
        proposed_hourly_rate=application.proposed_hourly_rate,
        availability_hours=mentor_profile.availability_hours.get('total_per_week') if mentor_profile and mentor_profile.availability_hours else None,
        experience_years=mentor_profile.experience_years if mentor_profile else None,
        education=mentor_profile.education if mentor_profile else None,
        current_position=mentor_profile.current_position if mentor_profile else None,
        linkedin_url=mentor_profile.linkedin_url if mentor_profile else None,
        resume_url=mentor_profile.resume_url if mentor_profile else None
    )


def reject_mentor_application(
    db: Session,
    institute_id: uuid.UUID,
    application_id: uuid.UUID,
    rejection_data: ApplicationRejectionRequest
) -> MentorApplicationOut:
    """Reject mentor application"""
    
    # Get application using the new MentorInstituteApplication model
    application = db.query(MentorInstituteApplication).options(
        joinedload(MentorInstituteApplication.mentor).joinedload(User.mentor_profile)
    ).filter(
        MentorInstituteApplication.id == application_id,
        MentorInstituteApplication.institute_id == institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()
    
    if not application:
        raise HTTPException(status_code=404, detail="Application not found or already processed")
    
    # Update application
    application.status = "rejected"
    application.responded_at = datetime.now(timezone.utc)
    application.response_message = rejection_data.rejection_message
    
    db.commit()
    db.refresh(application)
    
    # Convert to response format
    mentor = application.mentor
    mentor_profile = mentor.mentor_profile
    
    return MentorApplicationOut(
        id=application.id,
        applicant_id=application.mentor_id,
        applicant_name=f"{mentor.first_name} {mentor.last_name}",
        applicant_email=mentor.email,
        status=application.status,
        application_date=application.applied_at,
        application_message=application.application_message,
        expertise=mentor_profile.expertise_areas if mentor_profile and mentor_profile.expertise_areas else [],
        proposed_hourly_rate=application.proposed_hourly_rate,
        availability_hours=mentor_profile.availability_hours.get('total_per_week') if mentor_profile and mentor_profile.availability_hours else None,
        experience_years=mentor_profile.experience_years if mentor_profile else None,
        education=mentor_profile.education if mentor_profile else None,
        current_position=mentor_profile.current_position if mentor_profile else None,
        linkedin_url=mentor_profile.linkedin_url if mentor_profile else None,
        resume_url=mentor_profile.resume_url if mentor_profile else None
    )


def invite_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invitation_data: MentorInvitationCreate
) -> MentorInvitationOut:
    """Invite mentor to join institute"""
    
    # Check if mentor exists
    mentor = db.query(User).filter(
        User.id == invitation_data.mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # TEMPORARY FIX: Only require email verification, remove profile verification requirement
    if not mentor.is_email_verified:
        raise HTTPException(status_code=400, detail="Mentor must verify email before receiving invitations")

    # REMOVED: Profile verification requirement for invitations (temporary fix)
    # if not mentor.mentor_profile.is_verified:
    #     raise HTTPException(status_code=400, detail="Mentor must be verified to receive invitations")
    
    # Check if active association already exists
    existing_association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor.id,
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active"
    ).first()

    if existing_association:
        raise HTTPException(status_code=400, detail="Active association already exists with this mentor")

    # Check if pending invitation already exists
    existing_invite = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.mentor_id == mentor.id,
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invitation already exists for this mentor")
    
    # Create invitation using the new MentorInstituteInvite model
    invitation = MentorInstituteInvite(
        mentor_id=mentor.id,
        institute_id=institute_id,
        mentor_email=mentor.email,  # Use mentor's email from User record
        invitation_message=invitation_data.invitation_message,
        status="pending",
        proposed_hourly_rate=invitation_data.proposed_hourly_rate,
        proposed_hours_per_week=invitation_data.proposed_hours_per_week,
        expertise_areas_needed=invitation_data.expertise_areas_needed or [],
        contract_terms=invitation_data.contract_terms,
        invited_at=datetime.now(timezone.utc),
        expires_at=datetime.now(timezone.utc) + timedelta(days=30)
    )
    
    db.add(invitation)
    db.commit()
    db.refresh(invitation)
    
    return MentorInvitationOut(
        id=invitation.id,
        mentor_id=mentor.id if mentor else None,
        mentor_email=mentor.email,
        mentor_name=mentor.username if mentor else None,
        status=invitation.status,
        invitation_message=invitation_data.invitation_message,
        proposed_hourly_rate=invitation_data.proposed_hourly_rate,
        proposed_hours_per_week=invitation_data.proposed_hours_per_week,
        expertise_areas_needed=invitation_data.expertise_areas_needed,
        invited_at=invitation.invited_at,
        responded_at=invitation.responded_at,
        expires_at=invitation.expires_at
    )


def get_institute_mentors(
    db: Session,
    institute_id: uuid.UUID,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20
) -> InstituteMentorsResponse:
    """Get mentors associated with institute"""

    # Build query
    query = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor).joinedload(User.mentor_profile)
    ).filter(MentorInstituteAssociation.institute_id == institute_id)

    if status:
        query = query.filter(MentorInstituteAssociation.status == status)

    # Get total count
    total = query.count()

    # Get mentors with pagination - use start_date instead of approved_at
    associations = query.order_by(desc(MentorInstituteAssociation.start_date)).offset(skip).limit(limit).all()

    # Convert to response format
    mentor_list = []
    for assoc in associations:
        mentor = assoc.mentor
        mentor_profile = mentor.mentor_profile if mentor else None

        mentor_list.append(InstituteMentorOut(
            id=mentor.id,
            first_name=mentor.first_name,
            last_name=mentor.last_name,
            email=mentor.email,
            phone=mentor.mobile,
            status=assoc.status,
            join_date=assoc.start_date,
            expertise=mentor_profile.expertise_areas if mentor_profile and mentor_profile.expertise_areas else [],
            hourly_rate=assoc.hourly_rate,
            hours_per_week=mentor_profile.availability_hours.get('total_per_week') if mentor_profile and mentor_profile.availability_hours else None,
            students_assigned=0,  # Placeholder - would need student assignment system
            average_rating=mentor_profile.rating if mentor_profile else None,
            completed_sessions=0,  # Placeholder - would need session tracking
            total_earnings=Decimal("0.00"),  # Placeholder - would need payment tracking
            last_active=mentor.last_login if hasattr(mentor, 'last_login') else None,
            is_available=assoc.status == "approved"
        ))

    # Get status counts
    active = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "approved"
    ).count()

    inactive = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "inactive"
    ).count()

    pending = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "pending"
    ).count()

    return InstituteMentorsResponse(
        data=mentor_list,
        total=total,
        active=active,
        inactive=inactive,
        pending=pending
    )


def get_mentor_performance(
    db: Session,
    institute_id: uuid.UUID,
    mentor_id: uuid.UUID
) -> MentorPerformanceOut:
    """Get performance metrics for a specific mentor"""

    # Verify association exists
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.mentor_id == mentor_id
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Mentor not associated with this institute")

    # Get mentor details
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(User.id == mentor_id).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Calculate performance metrics (placeholder values - would need real session/assignment data)
    total_sessions = 50  # Placeholder
    completed_sessions = 45  # Placeholder
    cancelled_sessions = 5  # Placeholder

    # Get last 30 days activity
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    last_30_days_sessions = 8  # Placeholder

    return MentorPerformanceOut(
        mentor_id=mentor.id,
        mentor_name=f"{mentor.first_name} {mentor.last_name}",
        total_sessions=total_sessions,
        completed_sessions=completed_sessions,
        cancelled_sessions=cancelled_sessions,
        average_rating=mentor.mentor_profile.rating if mentor.mentor_profile else None,
        response_time_hours=2.5,  # Placeholder
        student_satisfaction=92.5,  # Placeholder
        revenue_generated=Decimal("3750.00"),  # Placeholder
        efficiency_score=88.5,  # Placeholder
        last_30_days_sessions=last_30_days_sessions
    )


def assign_mentor_to_competition(
    db: Session,
    institute_id: uuid.UUID,
    mentor_id: uuid.UUID,
    assignment_data: MentorAssignmentCreate
) -> MentorAssignmentOut:
    """Assign mentor to competition"""

    # Verify mentor is associated with institute
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.status == "approved"
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found or not approved for this institute")

    # Verify competition belongs to institute
    competition = db.query(Event).filter(
        Event.id == assignment_data.competition_id,
        Event.institute_id == institute_id
    ).first()

    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found or not owned by institute")

    # Check if assignment already exists
    existing_assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.competition_id == assignment_data.competition_id,
        CompetitionMentorAssignment.mentor_id == mentor_id
    ).first()

    if existing_assignment:
        raise HTTPException(status_code=400, detail="Mentor already assigned to this competition")

    # Create assignment
    assignment = CompetitionMentorAssignment(
        id=uuid.uuid4(),
        competition_id=assignment_data.competition_id,
        mentor_id=mentor_id,
        assigned_by=institute_id,
        status=MentorAssignmentStatusEnum.ASSIGNED,
        assigned_at=datetime.now(timezone.utc),
        estimated_hours=assignment_data.estimated_hours,
        special_instructions=assignment_data.special_instructions
    )

    db.add(assignment)
    db.commit()
    db.refresh(assignment)

    # Get mentor name
    mentor = db.query(User).filter(User.id == mentor_id).first()

    return MentorAssignmentOut(
        id=assignment.id,
        mentor_id=mentor_id,
        mentor_name=f"{mentor.first_name} {mentor.last_name}",
        competition_id=assignment_data.competition_id,
        competition_title=competition.title,
        status=assignment.status.value,
        assigned_at=assignment.assigned_at,
        accepted_at=assignment.accepted_at,
        completed_at=assignment.completed_at,
        estimated_hours=assignment.estimated_hours,
        actual_hours=assignment.actual_hours,
        progress_percentage=assignment.progress_percentage or 0.0,
        questions_assigned=assignment.total_questions or 0,
        questions_checked=assignment.questions_checked or 0
    )
